# 购物车到微信支付完整流程技术文档

## 概述

本文档详细分析了该项目从购物车结算到微信支付完成的完整技术流程，包括数据传递、API调用、支付处理和结果反馈等各个环节的具体实现。

## 1. 流程总览

整个支付流程分为以下几个主要阶段：

1. **购物车结算阶段**：用户选择商品并点击结算
2. **预下单阶段**：创建预订单并跳转到订单确认页面
3. **订单确认阶段**：用户确认订单信息并点击立即下单
4. **订单创建阶段**：创建正式订单
5. **支付页面阶段**：选择支付方式
6. **微信支付阶段**：调用微信支付接口
7. **支付结果处理阶段**：处理支付结果并跳转

## 2. 购物车结算阶段

### 2.1 结算按钮触发

**文件位置**：`pages/order_addcart/order_addcart.vue`

```vue
<form @submit="subOrder" report-submit='true'>
    <button class='placeOrder bg-color' formType="submit">去结算 ({{ bayCount }})</button>
</form>
```

### 2.2 结算处理逻辑

```javascript
subOrder: Debounce(function (event) {
    this.$store.commit("PRODUCT_TYPE", 'normal');
    uni.showLoading({
        title: '加载中...'
    });
    this.selectValue = [];
    
    // 收集选中商品ID
    this.cartList.valid.forEach(el => {
        el.cartInfoList.forEach(goods => {
            if (goods.check) {
                this.selectValue.push(goods.id)
            }
        })
    })
    
    if (this.selectValue.length > 0) {
        this.getPreOrder();
    } else {
        uni.hideLoading();
        return this.$util.Tips({
            title: '请选择产品'
        });
    }
}),
```

**关键步骤**：
1. 防抖处理（500ms）
2. 设置商品类型为普通商品
3. 显示加载提示
4. 收集选中商品ID
5. 验证是否有选中商品
6. 调用预下单方法

### 2.3 预下单数据准备

```javascript
getPreOrder: function () {
    let shoppingCartId = this.selectValue.map(item => {
        return {
            "shoppingCartId": Number(item)
        }
    })
    uni.hideLoading();
    onGetPreOrder("shoppingCart", shoppingCartId);
}
```

**数据转换**：
- 将商品ID数组转换为对象数组格式
- 每个对象包含 `shoppingCartId` 字段
- 确保ID为数字类型

## 3. 预下单阶段

### 3.1 预下单API调用

**文件位置**：`libs/order.js`

```javascript
export function onGetPreOrder(preOrderType, orderDetails) {
    return new Promise((resolve, reject) => {
        preOrderApi({
            "preOrderType": preOrderType,
            "orderDetails": orderDetails
        }).then(res => {
            uni.navigateTo({
                url: '/pages/goods/order_confirm/index?orderNo=' + res.data.orderNo
            });
        }).catch(err => {
            return util.Tips({
                title: err
            });
        })
    });
}
```

**API接口**：
- **请求地址**：`POST /api/front/order/pre/order`
- **请求参数**：
  - `preOrderType`: "shoppingCart"（标识为购物车下单）
  - `orderDetails`: 购物车商品ID数组
- **返回数据**：包含预订单号 `orderNo`

### 3.2 页面跳转

成功创建预订单后，自动跳转到订单确认页面：
```
/pages/goods/order_confirm/index?orderNo={预订单号}
```

## 4. 订单确认阶段

### 4.1 页面初始化

**文件位置**：`pages/goods/order_confirm/index.vue`

```javascript
onLoad(options) {
    this.orderNo = options.orderNo || 0;
    this.addressChangeId = parseInt(options.addressId) || 0;
    this.is_address = options.is_address ? true : false;
    if (this.isLogin) {
        this.getloadPreOrder();
    } else {
        toLogin();
    }
}
```

### 4.2 加载预订单详情

```javascript
getloadPreOrder: function() {
    loadPreOrderApi(this.orderNo).then(res => {
        let orderInfoVo = res.data;
        // 处理订单信息
        this.orderInfoVo = {
            ...orderInfoVo,
            systemFormValue: orderInfoVo.systemFormValue ? 
                this.$util.objToArr(JSON.parse(orderInfoVo.systemFormValue)) : []
        };
        this.merchantOrderVoList = orderInfoVo.merchantInfoList;
        // 设置其他订单相关数据...
    }).catch(err => {
        uni.navigateTo({
            url: '/pages/goods/order_list/index'
        });
    })
}
```

### 4.3 立即下单处理

```javascript
SubOrder: Debounce(function(e) {
    let that = this, data = {};
    
    // 1. 验证收货地址
    let flag = false;
    that.orderMerchantRequestList.map(function(v) {
        if (v.shippingType === 1 && !that.addressId && that.orderInfoVo.secondType !== 2) {
            flag = true;
        }
    });
    if (flag) {
        that.$util.Tips({ title: '请选择收货地址' });
        return;
    }
    
    // 2. 系统表单验证（省略详细验证逻辑）
    
    // 3. 构建订单数据
    data = {
        addressId: that.addressId,
        orderMerchantRequestList: that.orderMerchantRequestList,
        isUseIntegral: that.isUseIntegral,
        preOrderNo: that.orderNo,
        platUserCouponId: this.platUserCouponId,
        systemFormId: this.orderInfoVo.systemFormId,
        orderExtend: JSON.stringify(systemFormData)
    };
    
    uni.showLoading({ title: '订单提交中' });
    that.payment(data);
})
```

## 5. 订单创建阶段

### 5.1 订单创建API调用

```javascript
onCreate(data) {
    orderCreate(data).then(res => {
        if(res.data.groupBuyIsFull==1){
            uni.hideLoading();
            return this.$refs.sh_popup.open('center')
        }
        if (this.secondType === this.ProductTypeEnum.Integral && this.orderInfoVo.payFee == 0) {
            // 积分商品且金额为0，直接余额支付
            this.changeOrderPay(res.data.orderNo, 'yue', 'yue', 'integral', 'integral', '0')
        } else {
            // 普通商品，跳转支付页面
            this.getToPayment(this.secondType, res.data)
        }
        uni.hideLoading();
    }).catch(err=>{
        uni.hideLoading();
        return this.$util.Tips({ title: err });
    });
}
```

**API接口**：
- **请求地址**：`POST /api/front/order/create`
- **返回数据**：包含正式订单号和支付金额

### 5.2 跳转支付页面

```javascript
getToPayment(secondType,data) {
    let url = `/pages/goods/order_payment/index?orderNo=${data.orderNo}&payPrice=${data.payPrice}`
    uni.redirectTo({ url: url });
}
```

## 6. 支付页面阶段

### 6.1 支付页面初始化

**文件位置**：`pages/goods/order_payment/index.vue`

```javascript
onLoad(options) {
    this.payPrice = options.payPrice;
    this.orderNo = options.orderNo;
    this.fromType = options.fromType || ''
},
mounted() {
    this.payConfig();
}
```

### 6.2 支付配置加载

```javascript
payConfig() {
    uni.hideLoading();
    // 获取支付方式配置
    store.dispatch('getPayConfig').then((res) => {
        this.cartArr = res.payConfig;
        this.userBalance = res.userBalance;
        
        if (this.cartArr.length) {
            this.active = 0;
            this.payType = this.cartArr[0].value;
            this.isShow = false;
        } else {
            this.isShow = true;
            return this.$util.Tips({ title: '暂无支付方式！' })
        }
    });
}
```

**支付方式配置**（`store/modules/app.js`）：
```javascript
let cartArr = [
    {
        name: "微信支付",
        icon: "icon-a-ic_wechatpay",
        value: 'weixin',
        title: '微信快捷支付',
        payStatus: 1,
    },
    {
        name: "余额支付",
        icon: "icon-ic_Money2",
        value: 'yue',
        title: '可用余额:',
        payStatus: 1,
        userBalance: ''
    },
    {
        name: "支付宝支付",
        icon: "icon-a-ic_alipay",
        value: 'alipay',
        title: '支付宝快捷支付',
        payStatus: 1,
    }
];
```

### 6.3 支付方式选择

```javascript
payItem: Debounce(function(e,item) {
    let that = this;
    if(item.userBalance) that.userBalance = item.userBalance
    let active = e;
    that.active = active;
    that.animated = true;
    that.payType = that.cartArr[active].value;
    setTimeout(function() {
        that.car();
    }, 500);
})
```

### 6.4 支付渠道判断

```javascript
getPayCheck() {
    if (!this.payType) return this.$util.Tips({ title: '请选择支付方式' });

    if (this.payType === 'yue') {
        this.payChannel = 'yue'
    } else if (this.payType == 'alipay') {
        // #ifdef H5
        this.payChannel = 'alipay';
        // #endif
        // #ifdef APP-PLUS
        this.payChannel = 'alipayApp';
        // #endif
    } else {
        // 微信支付渠道判断
        // #ifdef H5
        this.payChannel = this.$wechat.isWeixin() ? 'public' : 'h5';
        // #endif
        // #ifdef APP-PLUS
        this.payChannel = this.systemPlatform === 'ios' ? 'wechatIos' : 'wechatAndroid';
        // #endif
        // #ifdef MP
        this.payChannel = this.productType == 'video' ? "video" : "mini";
        // #endif
    }
}
```

## 7. 微信支付阶段

### 7.1 立即支付处理

```javascript
getOrderPay: Debounce(function() {
    this.getPayCheck();
    if (Number(this.payPrice)>Number(this.userBalance) && this.payType === 'yue') {
        return this.$util.Tips({ title: '余额的金额不够，请切换支付方式' });
    }

    uni.showLoading({ title: '加载中...' });
    this.isBuy = true;

    // 调用订单支付
    this.changeOrderPay(this.orderNo, this.payChannel, this.payType, this.productType, this.fromType, this.payPrice)
})
```

### 7.2 订单支付API调用

**文件位置**：`mixins/OrderPay.js`

```javascript
changeOrderPay(orderNo, payChannel, payType, productType, fromType, payPrice) {
    orderPayApi({
        orderNo: orderNo,
        payChannel: payChannel,
        payType: payType,
        scene: productType === 'normal' ? 0 : 1177
    }).then(res => {
        this.handleOrderPay(res, orderNo, productType, fromType, payType, payPrice)
    }).catch(err => {
        uni.hideLoading();
        this.isBuy = false;
        return this.$util.Tips({ title: err });
    });
}
```

**API接口**：
- **请求地址**：`POST /api/front/pay/payment`
- **请求参数**：
  - `orderNo`: 订单号
  - `payChannel`: 支付渠道（public/h5/mini/wechatIos/wechatAndroid等）
  - `payType`: 支付类型（weixin/yue/alipay）
  - `scene`: 场景值（普通商品为0，视频号商品为1177）

### 7.3 支付结果处理

```javascript
handleOrderPay(res, orderNo, productType, fromType, payType, payPrice) {
    let jsConfig = res.data.jsConfig;
    let goPages = '/pages/goods/order_pay_status/index?order_id=' + orderNo;

    switch (res.data.payType) {
        case 'weixin':
            uni.hideLoading();
            this.weixinPay(jsConfig, orderNo, goPages, productType, fromType);
            break;
        case 'yue':
            uni.hideLoading();
            uni.reLaunch({ url: goPages + '&status=1' });
            break;
        case 'alipay':
            // 支付宝支付处理...
            break;
    }
}
```

### 7.4 微信支付调用（不同平台差异化处理）

#### 7.4.1 小程序微信支付

```javascript
// #ifdef MP
if (productType === 'video') {
    uni.requestOrderPayment({
        timeStamp: jsConfig.timeStamp,
        nonceStr: jsConfig.nonceStr,
        package: jsConfig.packages,
        signType: jsConfig.signType,
        paySign: jsConfig.paySign,
        ticket: jsConfig.ticket,
        success: function(ress) {
            setTimeout(res => {
                uni.hideLoading();
                uni.reLaunch({ url: goPages })
            }, 500);
        },
        fail: function(e) {
            uni.hideLoading();
            return this.$util.Tips({ title: '取消支付' }, {
                tab: 5, url: goPages + '&status=2'
            });
        }
    })
} else {
    uni.requestPayment({
        timeStamp: jsConfig.timeStamp,
        nonceStr: jsConfig.nonceStr,
        package: jsConfig.packages,
        signType: jsConfig.signType,
        paySign: jsConfig.paySign,
        success: function(ress) {
            setTimeout(res => {
                uni.hideLoading();
                uni.reLaunch({ url: goPages })
            }, 500);
        },
        fail: function(e) {
            uni.hideLoading();
            return this.$util.Tips({ title: '取消支付' }, {
                tab: 5, url: goPages + '&status=2'
            });
        }
    })
}
// #endif
```

#### 7.4.2 H5微信支付

```javascript
// #ifdef H5
let data = {
    timestamp: jsConfig.timeStamp,
    nonceStr: jsConfig.nonceStr,
    package: jsConfig.packages,
    signType: jsConfig.signType,
    paySign: jsConfig.paySign
};

if (this.$wechat.isWeixin()) {
    // 微信浏览器内支付
    this.$wechat.pay(data).then(res => {
        setTimeout(res => {
            uni.hideLoading();
            uni.redirectTo({ url: goPages })
        }, 500);
    }).catch(res => {
        uni.hideLoading();
        return this.$util.Tips({ title: '取消支付' }, {
            tab: 5, url: goPages + '&status=2'
        });
    });
} else {
    // 非微信浏览器，跳转H5支付
    setTimeout(() => {
        location.href = jsConfig.mwebUrl + '&redirect_url=' +
            window.location.protocol + '//' + window.location.host + goPages;
    }, 100)
    uni.hideLoading();
}
// #endif
```

#### 7.4.3 APP微信支付

```javascript
// #ifdef APP-PLUS
uni.requestPayment({
    provider: 'wxpay',
    orderInfo: {
        "appid": jsConfig.appId,
        "noncestr": jsConfig.nonceStr,
        "package": "Sign=WXPay",
        "partnerid": jsConfig.partnerid,
        "prepayid": jsConfig.prepayid,
        "timestamp": Number(jsConfig.timeStamp),
        "sign": jsConfig.paySign
    },
    success: function(res) {
        uni.hideLoading();
        setTimeout(res => {
            uni.redirectTo({ url: goPages })
        }, 500)
    },
    fail: function(err) {
        uni.hideLoading();
        uni.showModal({
            content: "支付失败",
            showCancel: false,
            confirmColor: '#f55850',
            success: function(res) {
                if (res.confirm) {
                    uni.redirectTo({ url: goPages + '&status=2' })
                }
            }
        })
    }
});
// #endif
```

## 8. 支付结果处理阶段

### 8.1 支付结果页面

**文件位置**：`pages/goods/order_pay_status/index.vue`

```javascript
onLoadFun: function() {
    if(this.fromType !=='svip') this.getOrderPayInfo();
}

getOrderPayInfo: function() {
    let that = this;
    uni.showLoading({ title: '正在加载中' });

    getOrderDetail(that.orderNo).then(res => {
        this.type = res.data.type
        that.$set(that, 'order_pay_info', res.data);

        if (res.data.payType === 'weixin') {
            // 微信支付需要查询支付结果
            setTimeout(()=>{
                that.wechatQueryPay('order');
            }, 2000);
        } else {
            // 其他支付方式直接根据订单状态判断
            if(res.data.paid){
                this.payResult = '支付成功';
                this.order_pay_info.paid = 1;
            } else {
                this.payResult = '支付失败';
                this.order_pay_info.paid = 2;
            }
            uni.hideLoading();
        }
    }).catch(err => {
        uni.hideLoading();
    });
}
```

### 8.2 微信支付结果查询

```javascript
wechatQueryPay(orderType) {
    wechatQueryPayResult({
        orderType: orderType,
        orderNo: this.orderNo
    }).then(res => {
        if(res.data){
            this.payResult = '支付成功';
            this.order_pay_info.paid = 1;
            uni.hideLoading();
            this.setPaidMember();
        } else {
            this.payResult = '支付失败';
            this.order_pay_info.paid = 2;
            uni.hideLoading();
        }
    }).catch(err => {
        this.order_pay_info.paid = 2;
        this.errMsg = true;
        this.msg = err;
        uni.hideLoading();
        this.$util.Tips({ title: err });
    });
}
```

**API接口**：
- **请求地址**：`GET /api/front/pay/query/wechat/payment/result`
- **请求参数**：
  - `orderType`: 订单类型
  - `orderNo`: 订单号

## 9. 流程图

### 9.1 整体流程图

```mermaid
flowchart TD
    A[用户点击购物车结算] --> B[防抖处理500ms]
    B --> C[收集选中商品ID]
    C --> D{是否有选中商品?}
    D -->|否| E[显示"请选择产品"错误]
    D -->|是| F[调用预下单API]
    F --> G{预下单成功?}
    G -->|否| H[显示错误信息]
    G -->|是| I[跳转订单确认页面]
    I --> J[加载预订单详情]
    J --> K[用户确认订单信息]
    K --> L[点击立即下单]
    L --> M[验证地址和表单]
    M --> N{验证通过?}
    N -->|否| O[显示验证错误]
    N -->|是| P[调用订单创建API]
    P --> Q{订单创建成功?}
    Q -->|否| R[显示创建失败]
    Q -->|是| S{是否积分商品且金额为0?}
    S -->|是| T[直接余额支付]
    S -->|否| U[跳转支付页面]
    U --> V[加载支付配置]
    V --> W[用户选择支付方式]
    W --> X[点击立即支付]
    X --> Y[调用支付API]
    Y --> Z{支付类型?}
    Z -->|微信支付| AA[调用微信支付]
    Z -->|余额支付| BB[余额扣款]
    Z -->|支付宝支付| CC[调用支付宝支付]
    AA --> DD[跳转支付结果页面]
    BB --> DD
    CC --> DD
    DD --> EE[查询支付结果]
    EE --> FF[显示支付状态]
```

### 9.2 微信支付平台差异化处理

```mermaid
flowchart TD
    A[微信支付调用] --> B{平台类型?}
    B -->|小程序| C{商品类型?}
    B -->|H5| D{是否微信浏览器?}
    B -->|APP| E[uni.requestPayment]

    C -->|视频号商品| F[uni.requestOrderPayment]
    C -->|普通商品| G[uni.requestPayment]

    D -->|是| H[微信JSSDK支付]
    D -->|否| I[跳转H5支付页面]

    F --> J[支付成功/失败回调]
    G --> J
    H --> J
    I --> J
    E --> J

    J --> K[跳转支付结果页面]

    style A fill:#e1f5fe
    style K fill:#c8e6c9
```

## 10. 关键技术点和注意事项

### 10.1 防抖处理
- 所有关键操作都使用了500ms防抖，防止用户重复点击
- 防抖函数位于 `utils/validate.js`

### 10.2 支付渠道判断
不同平台和环境下的支付渠道：
- **H5微信浏览器**：`public`
- **H5非微信浏览器**：`h5`
- **小程序普通商品**：`mini`
- **小程序视频号商品**：`video`
- **APP iOS**：`wechatIos`
- **APP Android**：`wechatAndroid`

### 10.3 支付参数构建
微信支付参数在不同平台下的差异：
- **小程序**：使用 `timeStamp`、`nonceStr`、`package`、`signType`、`paySign`
- **H5**：使用 `timestamp`、`nonceStr`、`package`、`signType`、`paySign`
- **APP**：使用 `appid`、`noncestr`、`package`、`partnerid`、`prepayid`、`timestamp`、`sign`

### 10.4 错误处理机制
- 每个API调用都有完整的错误处理
- 支付失败时会显示相应的错误信息
- 支付取消时会跳转到支付结果页面并标记状态

### 10.5 支付结果验证
- 微信支付需要额外调用查询接口验证支付结果
- 其他支付方式可直接根据订单状态判断
- 支付结果页面会自动查询最新的支付状态

## 11. 可能出现的问题和解决方案

### 11.1 支付配置问题
**问题**：支付方式不显示或显示异常
**解决方案**：
1. 检查后端支付配置是否正确开启
2. 确认 `manifest.json` 中的支付配置是否正确
3. 验证微信/支付宝的商户配置

### 11.2 支付参数错误
**问题**：支付调用失败，参数错误
**解决方案**：
1. 检查支付渠道判断逻辑是否正确
2. 确认不同平台下的参数格式是否匹配
3. 验证签名算法和密钥配置

### 11.3 支付结果查询失败
**问题**：支付完成但结果页面显示失败
**解决方案**：
1. 增加重试机制
2. 延长查询间隔时间
3. 提供手动刷新功能

### 11.4 页面跳转异常
**问题**：支付完成后页面跳转失败
**解决方案**：
1. 检查页面路径是否正确
2. 确认参数传递是否完整
3. 添加异常情况的兜底处理

## 12. 数据流转时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant Cart as 购物车页面
    participant PreAPI as 预下单API
    participant Confirm as 订单确认页面
    participant OrderAPI as 订单创建API
    participant Payment as 支付页面
    participant PayAPI as 支付API
    participant WeChat as 微信支付
    participant Result as 支付结果页面
    participant QueryAPI as 支付查询API

    U->>Cart: 点击结算按钮
    Cart->>Cart: 收集选中商品ID
    Cart->>PreAPI: 调用预下单接口
    PreAPI-->>Cart: 返回预订单号
    Cart->>Confirm: 跳转订单确认页面

    Confirm->>PreAPI: 加载预订单详情
    PreAPI-->>Confirm: 返回订单详情
    U->>Confirm: 点击立即下单
    Confirm->>OrderAPI: 创建正式订单
    OrderAPI-->>Confirm: 返回订单号和金额
    Confirm->>Payment: 跳转支付页面

    Payment->>PayAPI: 获取支付配置
    PayAPI-->>Payment: 返回支付方式列表
    U->>Payment: 选择微信支付并确认
    Payment->>PayAPI: 调用支付接口
    PayAPI-->>Payment: 返回支付参数
    Payment->>WeChat: 调用微信支付
    WeChat-->>Payment: 支付结果回调
    Payment->>Result: 跳转支付结果页面

    Result->>QueryAPI: 查询支付结果
    QueryAPI-->>Result: 返回支付状态
    Result->>U: 显示支付结果
```

这份技术文档详细描述了从购物车结算到微信支付完成的整个流程，包含了关键代码实现、API接口调用、平台差异化处理和错误处理机制，为开发和维护提供了完整的技术参考。

## 13. 核心方法调用关系图

### 13.1 支付流程核心方法调用关系

```mermaid
graph TD
    A[用户点击结算] --> B[subOrder - 购物车结算]
    B --> C[onGetPreOrder - 预下单处理]
    C --> D[preOrderApi - 预下单API]
    D --> E[订单确认页面加载]
    E --> F[SubOrder - 立即下单]
    F --> G[orderCreate - 订单创建API]
    G --> H[getToPayment - 跳转支付页面]
    H --> I[payConfig - 加载支付配置]
    I --> J[getOrderPay - 立即支付]
    J --> K[getPayCheck - 支付渠道判断]
    J --> L[changeOrderPay - 订单支付调度]
    L --> M[orderPayApi - 支付API调用]
    M --> N[handleOrderPay - 支付结果分发]
    N --> O{支付类型判断}
    O -->|微信支付| P[weixinPay - 微信支付处理]
    O -->|余额支付| Q[直接跳转结果页]
    O -->|支付宝支付| R[支付宝支付处理]
    P --> S{平台类型判断}
    S -->|小程序| T[uni.requestPayment/requestOrderPayment]
    S -->|H5| U[微信JSSDK支付/H5支付页面]
    S -->|APP| V[uni.requestPayment with wxpay]
    T --> W[支付结果回调]
    U --> W
    V --> W
    W --> X[跳转支付结果页面]
    X --> Y[getOrderPayInfo - 查询订单状态]
    Y --> Z{是否微信支付?}
    Z -->|是| AA[wechatQueryPay - 微信支付结果查询]
    Z -->|否| BB[直接显示支付状态]
    AA --> CC[wechatQueryPayResult - 支付查询API]
    CC --> DD[显示最终支付结果]
    BB --> DD

    %% style A fill:#e1f5fe
    %% style L fill:#ff9999
    %% style P fill:#ff9999
    %% style AA fill:#ffcc99
    %% style DD fill:#c8e6c9

    %% classDef apiClass fill:#fff2cc,stroke:#d6b656
    %% classDef coreMethod fill:#ffe6cc,stroke:#d79b00
    %% classDef platformMethod fill:#f8cecc,stroke:#b85450

    %% class D,G,M,CC apiClass
    %% class F,J,L,N coreMethod
    %% class P,T,U,V platformMethod
```

### 13.2 方法重要性层级图

```mermaid
graph TB
    subgraph "核心控制层 (最重要)"
        A1[changeOrderPay<br/>订单支付调度中心]
        A2[weixinPay<br/>微信支付平台处理]
        A3[SubOrder<br/>立即下单处理]
    end

    subgraph "业务处理层 (重要)"
        B1[getOrderPay<br/>立即支付处理]
        B2[handleOrderPay<br/>支付结果分发]
        B3[orderCreate<br/>订单创建API]
        B4[getPayCheck<br/>支付渠道判断]
    end

    subgraph "辅助功能层 (次重要)"
        C1[preOrderApi<br/>预下单API]
        C2[wechatQueryPay<br/>支付结果查询]
        C3[payConfig<br/>支付配置加载]
        C4[Debounce<br/>防抖处理]
    end

    A1 --> B1
    A1 --> B2
    A2 --> B4
    A3 --> B3
    B1 --> C1
    B2 --> C2
    B4 --> C3

    style A1 fill:#ff6b6b
    style A2 fill:#ff6b6b
    style A3 fill:#ff6b6b
    style B1 fill:#ffa726
    style B2 fill:#ffa726
    style B3 fill:#ffa726
    style B4 fill:#ffa726
    style C1 fill:#66bb6a
    style C2 fill:#66bb6a
    style C3 fill:#66bb6a
    style C4 fill:#66bb6a
```


## 14. 平台差异化方法分析

### 14.1 微信支付平台差异化处理详解

#### 14.1.1 小程序平台处理

**文件位置**：`mixins/OrderPay.js` 第125-198行

```javascript
// #ifdef MP
if (productType === 'video') {
    // 视频号商品支付
    uni.requestOrderPayment({
        timeStamp: jsConfig.timeStamp,
        nonceStr: jsConfig.nonceStr,
        package: jsConfig.packages,
        signType: jsConfig.signType,
        paySign: jsConfig.paySign,
        ticket: jsConfig.ticket,  // 视频号商品特有参数
        success: function(ress) {
            setTimeout(res => {
                uni.hideLoading();
                uni.reLaunch({ url: goPages })
            }, 500);
        },
        fail: function(e) {
            uni.hideLoading();
            return this.$util.Tips({ title: '取消支付' }, {
                tab: 5, url: goPages + '&status=2'
            });
        }
    })
} else {
    // 普通商品支付
    uni.requestPayment({
        timeStamp: jsConfig.timeStamp,
        nonceStr: jsConfig.nonceStr,
        package: jsConfig.packages,
        signType: jsConfig.signType,
        paySign: jsConfig.paySign,
        success: function(ress) {
            setTimeout(res => {
                uni.hideLoading();
                uni.reLaunch({ url: goPages })
            }, 500);
        },
        fail: function(e) {
            uni.hideLoading();
            return this.$util.Tips({ title: '取消支付' }, {
                tab: 5, url: goPages + '&status=2'
            });
        }
    })
}
// #endif
```

**关键特点**：
- **视频号商品**：使用 `uni.requestOrderPayment`，需要 `ticket` 参数
- **普通商品**：使用 `uni.requestPayment`
- **参数格式**：驼峰命名，如 `timeStamp`、`nonceStr`
- **回调处理**：统一的成功/失败回调机制

#### 14.1.2 H5平台处理

**文件位置**：`mixins/OrderPay.js` 第200-230行

```javascript
// #ifdef H5
let data = {
    timestamp: jsConfig.timeStamp,  // 注意：H5使用小写timestamp
    nonceStr: jsConfig.nonceStr,
    package: jsConfig.packages,
    signType: jsConfig.signType,
    paySign: jsConfig.paySign
};

if (this.$wechat.isWeixin()) {
    // 微信浏览器内支付 - 使用JSSDK
    this.$wechat.pay(data).then(res => {
        setTimeout(res => {
            uni.hideLoading();
            uni.redirectTo({ url: goPages })
        }, 500);
    }).catch(res => {
        uni.hideLoading();
        return this.$util.Tips({ title: '取消支付' }, {
            tab: 5, url: goPages + '&status=2'
        });
    });
} else {
    // 非微信浏览器 - 跳转H5支付页面
    setTimeout(() => {
        location.href = jsConfig.mwebUrl + '&redirect_url=' +
            window.location.protocol + '//' + window.location.host + goPages;
    }, 100)
    uni.hideLoading();
}
// #endif
```

**关键特点**：
- **微信浏览器内**：使用微信JSSDK的 `chooseWXPay` 接口
- **非微信浏览器**：跳转到微信H5支付页面
- **参数格式**：小写 `timestamp`（与小程序不同）
- **重定向处理**：支付完成后自动重定向回应用

#### 14.1.3 APP平台处理

**文件位置**：`mixins/OrderPay.js` 第232-273行

```javascript
// #ifdef APP-PLUS
uni.requestPayment({
    provider: 'wxpay',
    orderInfo: {
        "appid": jsConfig.appId,           // 微信开放平台AppId
        "noncestr": jsConfig.nonceStr,     // 注意：APP使用小写noncestr
        "package": "Sign=WXPay",           // 固定值
        "partnerid": jsConfig.partnerid,   // 微信支付商户号
        "prepayid": jsConfig.prepayid,     // 统一下单订单号
        "timestamp": Number(jsConfig.timeStamp), // 时间戳（数字类型）
        "sign": jsConfig.paySign           // 签名
    },
    success: function(res) {
        uni.hideLoading();
        setTimeout(res => {
            uni.redirectTo({ url: goPages })
        }, 500)
    },
    fail: function(err) {
        uni.hideLoading();
        uni.showModal({
            content: "支付失败",
            showCancel: false,
            confirmColor: '#f55850',
            success: function(res) {
                if (res.confirm) {
                    uni.redirectTo({ url: goPages + '&status=2' })
                }
            }
        })
    }
});
// #endif
```

**关键特点**：
- **支付方式**：使用 `uni.requestPayment` 配合 `provider: 'wxpay'`
- **参数格式**：全小写命名，如 `noncestr`、`timestamp`
- **特殊参数**：需要 `appid`、`partnerid`、`prepayid` 等APP特有参数
- **数据类型**：`timestamp` 需要转换为数字类型

### 14.2 平台差异对比表

#### 14.2.1 支付方法对比

| 平台 | 环境/商品类型 | 支付方法 | 关键参数 |
|------|---------------|----------|----------|
| 小程序 | 普通商品 | `uni.requestPayment` | `timeStamp`, `nonceStr`, `package`, `signType`, `paySign` |
| 小程序 | 视频号商品 | `uni.requestOrderPayment` | 上述参数 + `ticket` |
| H5 | 微信浏览器内 | `this.$wechat.pay` (JSSDK) | `timestamp`, `nonceStr`, `package`, `signType`, `paySign` |
| H5 | 非微信浏览器 | 页面跳转 | `mwebUrl` + `redirect_url` |
| APP | iOS/Android | `uni.requestPayment` | `appid`, `noncestr`, `package`, `partnerid`, `prepayid`, `timestamp`, `sign` |

#### 14.2.2 支付渠道映射表

| 平台 | 环境条件 | 支付渠道标识 | 后端处理方式 |
|------|----------|--------------|--------------|
| H5 | 微信浏览器 | `public` | 微信公众号支付 |
| H5 | 非微信浏览器 | `h5` | 微信H5支付 |
| 小程序 | 普通商品 | `mini` | 微信小程序支付 |
| 小程序 | 视频号商品 | `video` | 微信视频号支付 |
| APP | iOS系统 | `wechatIos` | 微信APP支付(iOS) |
| APP | Android系统 | `wechatAndroid` | 微信APP支付(Android) |

#### 14.2.3 参数格式差异表

| 参数名称 | 小程序格式 | H5格式 | APP格式 | 说明 |
|----------|------------|--------|---------|------|
| 时间戳 | `timeStamp` | `timestamp` | `timestamp` (Number) | 小程序驼峰，其他小写 |
| 随机字符串 | `nonceStr` | `nonceStr` | `noncestr` | APP全小写 |
| 订单详情 | `package` | `package` | `package` | 统一格式 |
| 签名类型 | `signType` | `signType` | - | APP不需要 |
| 签名 | `paySign` | `paySign` | `sign` | APP使用sign |
| 应用ID | - | - | `appid` | APP特有 |
| 商户号 | - | - | `partnerid` | APP特有 |
| 预支付ID | - | - | `prepayid` | APP特有 |
| 票据 | `ticket` (视频号) | - | - | 仅视频号商品 |

### 14.3 平台特殊处理逻辑

#### 14.3.1 支付渠道判断逻辑

**文件位置**：`pages/goods/order_payment/index.vue` 第145-174行

```javascript
getPayCheck() {
    if (!this.payType) return this.$util.Tips({ title: '请选择支付方式' });

    if (this.payType === 'yue') {
        this.payChannel = 'yue'
    } else if (this.payType == 'alipay') {
        // #ifdef H5
        this.payChannel = 'alipay';
        // #endif
        // #ifdef APP-PLUS
        this.payChannel = 'alipayApp';
        // #endif
    } else {
        // 微信支付渠道判断
        // #ifdef H5
        this.payChannel = this.$wechat.isWeixin() ? 'public' : 'h5';
        // #endif
        // #ifdef APP-PLUS
        this.payChannel = this.systemPlatform === 'ios' ? 'wechatIos' : 'wechatAndroid';
        // #endif
        // #ifdef MP
        this.payChannel = this.productType == 'video' ? "video" : "mini";
        // #endif
    }
}
```

#### 14.3.2 错误处理差异

| 平台 | 错误处理方式 | 特殊处理 |
|------|--------------|----------|
| 小程序 | `fail` 回调 + `complete` 回调 | 检查 `errMsg` 判断取消支付 |
| H5 | Promise `.catch()` | 微信浏览器和普通浏览器不同处理 |
| APP | `fail` 回调 | 使用 `uni.showModal` 显示错误 |

#### 14.3.3 成功回调处理差异

| 平台 | 跳转方式 | 延迟处理 |
|------|----------|----------|
| 小程序 | `uni.reLaunch` | 500ms延迟 |
| H5 | `uni.redirectTo` | 500ms延迟 |
| APP | `uni.redirectTo` | 500ms延迟 |

### 14.4 平台兼容性最佳实践

#### 14.4.1 统一的错误处理模式

```javascript
// 统一的错误处理函数
handlePaymentError(error, goPages) {
    uni.hideLoading();

    // 根据错误类型进行不同处理
    if (error.errMsg && error.errMsg.includes('cancel')) {
        // 用户取消支付
        return this.$util.Tips({ title: '取消支付' }, {
            tab: 5, url: goPages + '&status=2'
        });
    } else {
        // 支付失败
        uni.showModal({
            content: "支付失败",
            showCancel: false,
            confirmColor: '#f55850',
            success: function(res) {
                if (res.confirm) {
                    uni.redirectTo({ url: goPages + '&status=2' })
                }
            }
        })
    }
}
```

#### 14.4.2 参数标准化处理

```javascript
// 参数格式标准化
formatPaymentParams(jsConfig, platform) {
    const baseParams = {
        nonceStr: jsConfig.nonceStr,
        package: jsConfig.packages,
        paySign: jsConfig.paySign
    };

    switch(platform) {
        case 'mp':
            return {
                ...baseParams,
                timeStamp: jsConfig.timeStamp,
                signType: jsConfig.signType
            };
        case 'h5':
            return {
                ...baseParams,
                timestamp: jsConfig.timeStamp,
                signType: jsConfig.signType
            };
        case 'app':
            return {
                appid: jsConfig.appId,
                noncestr: jsConfig.nonceStr,
                package: "Sign=WXPay",
                partnerid: jsConfig.partnerid,
                prepayid: jsConfig.prepayid,
                timestamp: Number(jsConfig.timeStamp),
                sign: jsConfig.paySign
            };
    }
}
```

这些平台差异化处理确保了支付功能在不同平台下的兼容性和稳定性，是整个支付系统的重要组成部分。
